/**
 * Resume Builder Integration Tests
 * 
 * Comprehensive tests to verify all buttons, actions, and logic work correctly
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ResumeBuilder } from '@/components/resume-builder/ResumeBuilder';

// Mock dependencies
jest.mock('next-auth/react');
jest.mock('next/navigation');

const mockPush = jest.fn();
const mockGet = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
  
  (useSession as jest.Mock).mockReturnValue({
    data: { user: { email: '<EMAIL>' } },
    status: 'authenticated'
  });
  
  (useRouter as jest.Mock).mockReturnValue({
    push: mockPush
  });
  
  (useSearchParams as jest.Mock).mockReturnValue({
    get: mockGet
  });

  // Mock fetch
  global.fetch = jest.fn();
});

describe('Resume Builder Integration Tests', () => {
  describe('Main Page Navigation and Actions', () => {
    it('should render main page with create button', async () => {
      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: [] })
      });

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        expect(screen.getByText('My Resumes')).toBeInTheDocument();
        expect(screen.getByText('Create New Resume')).toBeInTheDocument();
      });
    });

    it('should handle create new resume button click', async () => {
      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: [] })
      });

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        const createButton = screen.getByText('Create New Resume');
        fireEvent.click(createButton);
        expect(mockPush).toHaveBeenCalledWith('/resume-builder?action=new');
      });
    });

    it('should handle edit resume button click', async () => {
      const mockResumes = [{
        id: 'resume-1',
        title: 'Test Resume',
        template: 'modern',
        isPublic: false,
        exportCount: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }];

      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockResumes })
      });

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        const editButton = screen.getByTitle('Edit');
        fireEvent.click(editButton);
        expect(mockPush).toHaveBeenCalledWith('/resume-builder?action=edit&id=resume-1');
      });
    });

    it('should handle preview resume button click', async () => {
      const mockResumes = [{
        id: 'resume-1',
        title: 'Test Resume',
        template: 'modern',
        isPublic: false,
        exportCount: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }];

      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockResumes })
      });

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        const previewButton = screen.getByTitle('Preview Resume');
        fireEvent.click(previewButton);
        expect(mockPush).toHaveBeenCalledWith('/resume-builder?action=preview&id=resume-1');
      });
    });

    it('should handle download resume button click', async () => {
      const mockResumes = [{
        id: 'resume-1',
        title: 'Test Resume',
        template: 'modern',
        isPublic: false,
        exportCount: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }];

      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockResumes })
      });

      // Mock alert
      window.alert = jest.fn();

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        const downloadButton = screen.getByTitle('Download Resume');
        fireEvent.click(downloadButton);
        expect(window.alert).toHaveBeenCalledWith(
          'Download functionality is coming soon! You can preview and copy your resume content for now.'
        );
      });
    });

    it('should handle delete resume with confirmation', async () => {
      const mockResumes = [{
        id: 'resume-1',
        title: 'Test Resume',
        template: 'modern',
        isPublic: false,
        exportCount: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }];

      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: mockResumes })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true })
        });

      // Mock confirm
      window.confirm = jest.fn().mockReturnValue(true);

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        const deleteButton = screen.getByRole('button', { name: /trash/i });
        fireEvent.click(deleteButton);
        
        expect(window.confirm).toHaveBeenCalledWith(
          'Are you sure you want to delete this resume? This action cannot be undone.'
        );
      });
    });
  });

  describe('Resume Builder Component Actions', () => {
    it('should render resume builder with all tabs', () => {
      const mockResume = {
        title: 'Test Resume',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: [],
        template: 'modern',
        isPublic: false
      };

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
      expect(screen.getByText('Personal')).toBeInTheDocument();
      expect(screen.getByText('Experience')).toBeInTheDocument();
      expect(screen.getByText('Education')).toBeInTheDocument();
      expect(screen.getByText('Skills')).toBeInTheDocument();
    });

    it('should handle save button click', async () => {
      const mockOnSave = jest.fn();
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            id: 'new-resume-id',
            title: 'Test Resume',
            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            experience: [],
            education: [],
            skills: [],
            template: 'modern',
            isPublic: false
          }
        })
      });

      render(
        <ResumeBuilder
          onSave={mockOnSave}
          onCancel={jest.fn()}
        />
      );

      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/resume-builder', expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }));
      });
    });

    it('should handle cancel button click', () => {
      const mockOnCancel = jest.fn();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('should handle preview button click', () => {
      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      const previewButton = screen.getByText('Preview');
      fireEvent.click(previewButton);

      expect(screen.getByText('Resume Preview')).toBeInTheDocument();
    });

    it('should handle template selection', async () => {
      const user = userEvent.setup();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      const templateSelect = screen.getByDisplayValue('Modern');
      await user.click(templateSelect);

      const classicOption = screen.getByText('Classic');
      await user.click(classicOption);

      expect(screen.getByDisplayValue('Classic')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('should handle personal info form updates', async () => {
      const user = userEvent.setup();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      const firstNameInput = screen.getByLabelText('First Name *');
      await user.type(firstNameInput, 'John');

      expect(firstNameInput).toHaveValue('John');
    });

    it('should handle experience form - add new experience', async () => {
      const user = userEvent.setup();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      // Switch to experience tab
      const experienceTab = screen.getByText('Experience');
      await user.click(experienceTab);

      // Add new experience
      const addButton = screen.getByText('Add Experience');
      await user.click(addButton);

      expect(screen.getByText('New Position')).toBeInTheDocument();
    });

    it('should handle education form - add new education', async () => {
      const user = userEvent.setup();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      // Switch to education tab
      const educationTab = screen.getByText('Education');
      await user.click(educationTab);

      // Add new education
      const addButton = screen.getByText('Add Education');
      await user.click(addButton);

      expect(screen.getByText('New Degree')).toBeInTheDocument();
    });

    it('should handle skills form - add new skill', async () => {
      const user = userEvent.setup();

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      // Switch to skills tab
      const skillsTab = screen.getByText('Skills');
      await user.click(skillsTab);

      // Add new skill
      const skillNameInput = screen.getByLabelText('Skill Name');
      await user.type(skillNameInput, 'JavaScript');

      const addButton = screen.getByText('Add Skill');
      await user.click(addButton);

      expect(screen.getByText('JavaScript')).toBeInTheDocument();
    });
  });

  describe('URL Parameter Handling', () => {
    it('should handle action=new parameter', () => {
      mockGet.mockImplementation((param) => {
        if (param === 'action') return 'new';
        return null;
      });

      render(<ResumeBuilderPage />);

      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
    });

    it('should handle action=edit parameter', () => {
      mockGet.mockImplementation((param) => {
        if (param === 'action') return 'edit';
        if (param === 'id') return 'resume-1';
        return null;
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            id: 'resume-1',
            title: 'Test Resume',
            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            experience: [],
            education: [],
            skills: [],
            template: 'modern',
            isPublic: false
          }
        })
      });

      render(<ResumeBuilderPage />);

      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
    });

    it('should handle action=preview parameter', () => {
      mockGet.mockImplementation((param) => {
        if (param === 'action') return 'preview';
        if (param === 'id') return 'resume-1';
        return null;
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            id: 'resume-1',
            title: 'Test Resume',
            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            experience: [],
            education: [],
            skills: [],
            template: 'modern',
            isPublic: false
          }
        })
      });

      render(<ResumeBuilderPage />);

      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockGet.mockReturnValue(null);
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

      render(<ResumeBuilderPage />);

      await waitFor(() => {
        expect(screen.getByText(/Failed to load resumes/)).toBeInTheDocument();
      });
    });

    it('should handle save errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Save Error'));

      render(
        <ResumeBuilder
          onSave={jest.fn()}
          onCancel={jest.fn()}
        />
      );

      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/Failed to save resume/)).toBeInTheDocument();
      });
    });
  });
});
